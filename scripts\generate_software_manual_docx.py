# -*- coding: utf-8 -*-
"""
生成《软件说明书》Word文档（用于软著提交）
使用方法：
  1) pip install python-docx
  2) python scripts/generate_software_manual_docx.py
生成路径：docs/软件说明书-联邦学习数据交易区块链与IPFS集成系统-V1.0.docx
"""
import os
from datetime import datetime
from docx import Document
from docx.shared import Pt
from docx.oxml.ns import qn
from docx.enum.text import WD_ALIGN_PARAGRAPH

TITLE_CN = "联邦学习数据交易区块链与IPFS集成系统"
TITLE_EN = "Federated Learning Data Trading Blockchain and IPFS Integration System"
VERSION = "V1.0"
OUTPUT_DIR = os.path.join("docs")
OUTPUT_PATH = os.path.join(OUTPUT_DIR, f"软件说明书-{TITLE_CN}-{VERSION}.docx")

# 可根据需要填写
AUTHOR = "【填写申请人姓名/单位】"
CONTACT = "【电话/邮箱】"
FINISH_DATE = datetime.now().strftime("%Y-%m-%d")


def set_styles(document: Document):
    # 全局中文字体为“宋体”
    style = document.styles['Normal']
    font = style.font
    font.name = '宋体'
    font.size = Pt(11)
    # 兼容中文字体
    style.element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')


def add_title_page(document: Document):
    p = document.add_paragraph()
    p.alignment = WD_ALIGN_PARAGRAPH.CENTER
    run = p.add_run(f"{TITLE_CN}\n{TITLE_EN}")
    run.font.size = Pt(20)
    run.bold = True

    p2 = document.add_paragraph()
    p2.alignment = WD_ALIGN_PARAGRAPH.CENTER
    run2 = p2.add_run(f"软件说明书 {VERSION}")
    run2.font.size = Pt(14)

    document.add_paragraph("")
    info = document.add_paragraph()
    info.add_run(f"开发者/单位：{AUTHOR}\n")
    info.add_run(f"联系方式：{CONTACT}\n")
    info.add_run(f"完成日期：{FINISH_DATE}")

    document.add_page_break()


def add_heading(document: Document, text: str, level: int = 1):
    document.add_heading(text, level=level)


def add_paragraphs(document: Document, content: str):
    # 将多段文本按\n拆分为段落
    for line in content.strip().split('\n'):
        document.add_paragraph(line.strip())


SECTIONS = [
    ("1. 软件名称与版本", f"""
- 中文名称：{TITLE_CN}
- 英文名称：{TITLE_EN}
- 版本号：{VERSION}
- 简称：FL-DataTrade-BC-IPFS
"""),
    ("2. 开发单位/作者", f"""
- 开发者/单位：{AUTHOR}
- 联系方式：{CONTACT}
- 完成日期：{FINISH_DATE}
"""),
    ("3. 软件简介", """
本软件结合联邦学习、区块链与IPFS分布式存储，面向“数据不出域”的隐私计算与数据/模型交易场景，提供任务与参与者管理、分期支付与奖励分配、训练监控、模型上链确权与分布式存储等端到端能力。训练完成后将最终模型上传至IPFS，并在区块链记录模型哈希与性能指标，确保结果可验证与不可篡改。适用于金融风控、医疗影像、物联网等多机构多主体联合建模应用。
"""),
    ("一、运行支持环境/支持软件", """
1) 操作系统：Windows 10/11、macOS 10.15+、Ubuntu 18.04+
2) 浏览器：Chrome 90+ / Firefox 88+ / Safari 14+ / Edge 90+
3) 运行时：Node.js 14+、Python 3.8+、MySQL 8.0+
4) 区块链：Truffle 5.x、Ganache 7.0+、MetaMask
5) 分布式存储：go-ipfs 0.17.0+（ipfs daemon）
6) AI/联邦学习依赖：PyTorch、Flower (flwr)
7) Web 前端：Vue3、Vite、Element Plus、Web3.js、Axios
"""),
    ("二、系统总体设计", """
系统由前端（frontend/）、后端API（backend/）、联邦学习服务（flower/）、区块链合约（blockchain/）与IPFS存储组成：
- 前端：任务/交易/训练/模型的可视化与操作界面；集成Web3钱包交互。
- 后端：提供任务、模型、参与者、交易、日志等REST API，并代理IPFS上传/下载。
- 联邦学习：基于Flower+PyTorch实现训练编排、指标聚合与训练完成后模型导出。
- 区块链：Solidity智能合约记录训练状态、参与者与最终模型信息；确权与追溯。
- IPFS：最终模型与元数据分布式存储；链上记录IPFS哈希确保可验证分发。
"""),
    ("三、数据与数据库设计（MySQL）", """
主要数据表：
- tasks：任务（奖励、币种、分期比例、平台费率、训练超时、状态等）
- participants：参与者（client_address、状态、数据规模等）
- models：模型（类型、版本、性能、文件路径、ipfs_hash、from_training 等）
- metrics、participant_metrics：训练指标（accuracy/loss/样本数/轮次等）
- transactions：交易记录（类型、币种、金额、状态、blockchain_tx_hash、wallet_type）
- logs：系统与任务日志；users：用户信息；ipfs_files：IPFS文件登记
与数据交易相关要点：
- 交易类型：first_payment、second_payment、platform_fee、participant_reward
- 链上支付字段：blockchain_tx_hash、wallet_type
- IPFS字段：models.ipfs_hash、ipfs_uploaded、ipfs_metadata
"""), 
    ("四、业务流程", """
1) 任务创建与首期付款：设置奖励/分期/费率后，生成首期付款记录（可链上支付）。
2) 参与者申请与训练启动：审核达标后触发训练；Flower服务端下发配置并聚合指标。
3) 训练过程与链上状态：记录当前轮次、活跃参与者；轮次完成后生成模型。
4) 模型存储与确权：模型与元数据上传IPFS，合约存证 modelHash/accuracy/contributors。
5) 二期付款与模型下载：校验支付后开放下载（可经后端API代理IPFS下载）。
6) 奖励分配：90%奖励按贡献派发；平台费10%在首/二期计提。
"""),
    ("五、主要功能模块", """
A. 交易与支付（backend/models/Transaction.js）
- 交易记录创建/查询，首期/二期/平台费/奖励等类型齐全；
- 支持链上支付哈希与钱包类型落库；金额正负表示收/支。

B. 模型与IPFS
- 模型记录管理（name/type/version/performance/file_path/is_latest 等）；
- IPFS接口：/api/ipfs/status|upload|download|info；
- 专用接口：/api/models/:id/upload-to-ipfs。

C. 联邦学习（flower/server.py, flower/client.py）
- FedAvg、最小参与者约束、超时控制；
- 指标加权聚合；训练完成自动导出并上传IPFS；合约存证。

D. 区块链（blockchain/contracts/FederatedLearningContract.sol）
- 账户注册与角色校验；训练状态/结果记录；服务器地址授权。

E. 前端
- 任务/参与者/训练/交易/模型管理；Web3钱包交互与权限控制。
"""),
    ("六、对外接口（选摘）", """
任务：GET/POST/PUT/DELETE /api/tasks；POST /api/tasks/:id/start；/complete
参与者：/api/participants；/api/tasks/:taskId/participants
模型：/api/models；/api/models/:id；POST /api/models/:id/upload-to-ipfs
IPFS：/api/ipfs/status、/upload、/download/:hash、/info/:hash
合约：startTraining、isTrainingActive、getCurrentRound、storeFinalModel 等
"""),
    ("七、安装与部署", """
环境准备：
- Python：pip install flwr torch torchvision web3 flask flask-cors numpy pandas ipfshttpclient
- Node：npm install（根目录与 frontend/）
- 区块链：npm i -g truffle ganache；启动Ganache
- MySQL：启动并配置连接
- IPFS：安装后 ipfs init、ipfs daemon
一键启动：python start_system.py（依赖检查→Ganache→合约→FL服务/客户端→后端→前端）
"""),
    ("八、使用说明", """
管理员：登录→创建任务（奖励/分期/费率/训练参数）→审核参与者→开始训练→训练完成自动上链+IPFS→发起二期付款→允许下载→奖励分配→查看交易与日志。
客户端：连接钱包→申请参与→审核通过→接收指令本地训练→查看贡献与奖励。
"""),
    ("九、安全与合规", """
- 隐私保护：联邦学习避免原始数据外泄；
- 可追溯性：训练状态与结果上链不可篡改；
- 完整性：模型IPFS哈希可校验；
- 支付安全：链上交易，保存哈希与钱包类型；
- 权限控制：合约onlyServer；前端按角色控制界面。
"""),
    ("十、特色与创新点", """
- 面向数据/模型交易的联邦学习一体化方案（分期支付/平台费/奖励完整闭环）；
- 区块链+IPFS双确权：过程可信、结果可验证与可分发；
- 最小参与者与超时控制，强调工程实用性；
- 前后端联动与脚本化，一键运行。
"""),
    ("十一、性能与限制（建议项）", """
- 训练耗时与参与者数量、数据量、网络条件相关；
- IPFS速率受网关与pin策略影响；
- 公链/测试网交互延迟较本地链更高；
- 后端API可结合PM2/Nginx扩展并发能力。
"""),
    ("十二、版本与维护", f"""
- 当前版本：{VERSION}
- 规划：多链兼容、更完善的贡献度与防投毒、模型签名与可验证推理。
"""),
    ("十三、附录A：源代码目录（节选）", """
- flower/（联邦学习服务端/客户端）
- frontend/（Vue3 + Vite）
- backend/（Express + MySQL；models/services/db）
- blockchain/（Solidity + Truffle）
- start_system.py（一键启动脚本）
- README*.md（说明文档）
"""),
    ("十四、附录B：源程序选取建议（软著提交）", """
前60页：flower/server.py、flower/client.py、blockchain/contracts/FederatedLearningContract.sol
后60页：backend/app.js、frontend/src/views/server/CreateTask.vue、frontend/src/services/web3.js、frontend/src/services/api.js、（必要时）start_system.py片段
说明：每页≥50行，页眉页脚与连续行号，文件头含版权声明。
"""),
    ("十五、附录C：运行与截图清单（建议）", """
- 任务创建与支付配置、参与者审核、训练监控、交易列表、模型列表（含IPFS哈希）、链上交易哈希、IPFS状态页。
"""),
    ("十六、版权与授权说明", f"""
- 著作权归属：{AUTHOR}
- 授权策略：可选MIT/专有/商用授权（与依赖许可证兼容）
- 第三方组件：遵循PyTorch、Flower、Vue、Express、Truffle、go-ipfs等开源许可证。
"""),
]


def build_document():
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR, exist_ok=True)

    doc = Document()
    set_styles(doc)
    add_title_page(doc)

    for title, body in SECTIONS:
        add_heading(doc, title, level=1)
        add_paragraphs(doc, body)
        doc.add_paragraph("")

    doc.save(OUTPUT_PATH)
    print(f"✅ 已生成：{OUTPUT_PATH}")


if __name__ == "__main__":
    build_document()

